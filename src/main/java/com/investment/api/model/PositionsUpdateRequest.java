package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Request model for updating OHLCV data for position symbols.
 */
@Schema(description = "Request for updating OHLCV data for position symbols")
public class PositionsUpdateRequest {

    @Schema(description = "Whether to perform a dry run (validation only) without actual updates", 
            example = "false", defaultValue = "false")
    private boolean dryRun = false;

    @Schema(description = "Whether to recalculate P&L after OHLCV updates", 
            example = "true", defaultValue = "true")
    private boolean recalculatePnL = true;

    public PositionsUpdateRequest() {
    }

    public PositionsUpdateRequest(boolean dryRun, boolean recalculatePnL) {
        this.dryRun = dryRun;
        this.recalculatePnL = recalculatePnL;
    }

    public boolean isDryRun() {
        return dryRun;
    }

    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }

    public boolean isRecalculatePnL() {
        return recalculatePnL;
    }

    public void setRecalculatePnL(boolean recalculatePnL) {
        this.recalculatePnL = recalculatePnL;
    }

    @Override
    public String toString() {
        return "PositionsUpdateRequest{" +
                "dryRun=" + dryRun +
                ", recalculatePnL=" + recalculatePnL +
                '}';
    }
}
